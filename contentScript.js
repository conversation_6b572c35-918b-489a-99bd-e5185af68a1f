// contentScript.js
(() => {
  console.log('Agent AI Content Script loaded on:', window.location.href);
  let lastConvo = '';

  function fetchConversation() {
    console.log('Fetching conversation...');

    // Essayer plusieurs sélecteurs pour Instagram
    const selectors = [
      'div[role="grid"][aria-label*="Messages"]',
      'div[role="grid"]',
      '[data-testid="conversation-messages"]',
      '.x1n2onr6', // Classe Instagram commune
      '[role="main"] div[role="grid"]'
    ];

    let grid = null;
    for (const selector of selectors) {
      grid = document.querySelector(selector);
      if (grid) {
        console.log('Found conversation grid with selector:', selector);
        break;
      }
    }

    if (!grid) {
      console.log('No conversation grid found');
      return '';
    }

    const texts = [];

    // Essayer différentes méthodes pour extraire les messages
    const messageSelectors = [
      'div[role="row"] div[dir="auto"]',
      'div[role="row"] span',
      '[data-testid="message-text"]',
      '.x1lliihq', // Classe texte Instagram
      'div[role="row"] div'
    ];

    for (const msgSelector of messageSelectors) {
      const elements = grid.querySelectorAll(msgSelector);
      if (elements.length > 0) {
        console.log(`Found ${elements.length} message elements with selector:`, msgSelector);
        elements.forEach(el => {
          const text = el.textContent.trim();
          if (text && text.length > 0 && !texts.includes(text)) {
            texts.push(text);
          }
        });
        break;
      }
    }

    console.log('Extracted texts:', texts);
    return texts.join('\n');
  }

  function sendConversationIfNew() {
    const convo = fetchConversation();
    console.log('Current conversation length:', convo.length);

    if (!convo || convo === lastConvo) {
      console.log('No new conversation to send');
      return;
    }

    lastConvo = convo;
    console.log('Sending new conversation to background script');

    try {
      if (chrome && chrome.runtime && chrome.runtime.sendMessage) {
        chrome.runtime.sendMessage({ action: 'updateConvo', convo: lastConvo });
        console.log('Conversation sent successfully');
      } else {
        console.error('Chrome runtime not available');
      }
    } catch (err) {
      console.error('Failed to send conversation to background:', err);
    }
  }

  // Attendre que la page soit chargée
  function init() {
    console.log('Initializing Agent AI content script...');

    // Vérifier si on est sur une page de messages
    const isMessagesPage = window.location.href.includes('/direct/') ||
                          document.querySelector('[aria-label*="Messages"]') ||
                          document.querySelector('[data-testid="conversation"]');

    if (!isMessagesPage) {
      console.log('Not on messages page, waiting...');
      setTimeout(init, 2000);
      return;
    }

    console.log('On messages page, starting observation...');

    // Observer les changements DOM
    const observer = new MutationObserver(() => {
      sendConversationIfNew();
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      characterData: true
    });

    // Envoi initial
    setTimeout(sendConversationIfNew, 1000);

    // Vérification périodique
    setInterval(sendConversationIfNew, 5000);
  }

  // Démarrer quand le DOM est prêt
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }
})();


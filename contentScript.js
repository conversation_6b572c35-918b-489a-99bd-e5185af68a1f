// contentScript.js
(() => {
  let lastConvo = '';

  function fetchConversation() {
    const grid = document.querySelector('div[role="grid"][aria-label*="Messages"]');
    if (!grid) return '';
    const texts = [];
    grid.querySelectorAll('div[role="row"]').forEach(row => {
      row.querySelectorAll('div[dir="auto"]').forEach(b => {
        const t = b.textContent.trim();
        if (t && !texts.includes(t)) texts.push(t);
      });
    });
    return texts.join('\n');
  }

  function sendConversationIfNew() {
    const convo = fetchConversation();
    if (!convo || convo === lastConvo) return;
    lastConvo = convo;
    try {
      if (chrome && chrome.runtime && chrome.runtime.sendMessage) {
        chrome.runtime.sendMessage({ action: 'updateConvo', convo: lastConvo });
      }
    } catch (err) {
      console.error('Failed to send conversation to background:', err);
    }
  }

  // Observe all DOM changes to catch new messages or scrolling
  const observer = new MutationObserver(sendConversationIfNew);
  observer.observe(document.body, { childList: true, subtree: true });

  // Initial send
  sendConversationIfNew();
})();


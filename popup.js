document.addEventListener('DOMContentLoaded', async function() {
  // Éléments DOM
  const quotaEl = document.getElementById('quota');
  const summaryText = document.getElementById('summary-text');
  const loadingSummary = document.getElementById('loading-summary');
  const loadingKeypoints = document.getElementById('loading-keypoints');
  const keypointsContainer = document.getElementById('keypoints-container');
  const emptyStateKeypoints = document.getElementById('empty-state-keypoints');

  // Éléments pour les questions
  const questionInput = document.getElementById('question-input');
  const askButton = document.getElementById('ask-button');
  const loadingQuestion = document.getElementById('loading-question');
  const questionAnswer = document.getElementById('question-answer');
  const answerText = document.getElementById('answer-text');
  const copyAnswerBtn = document.getElementById('copy-answer');

  let lastConvo = '';
  let copiedTimeouts = new Map();

  // Initialiser les données
  async function initializeData() {
    try {
      const data = await new Promise(resolve => {
        chrome.storage.local.get(['used', 'limit', 'lastConvo'], resolve);
      });

      // Mettre à jour le quota
      quotaEl.textContent = `${data.used || 0}/${data.limit || 100}`;
      lastConvo = data.lastConvo || '';

      console.log('Data initialized:', data);

      if (lastConvo) {
        await loadAI();
      }
    } catch (error) {
      console.error('Error initializing data:', error);
    }
  }

  // Charger l'IA
  async function loadAI() {
    if (!lastConvo) {
      summaryText.textContent = 'Aucune conversation détectée.';
      keypointsContainer.innerHTML = '';
      emptyStateKeypoints.style.display = 'block';
      return;
    }

    // Afficher les états de chargement
    loadingSummary.style.display = 'flex';
    loadingKeypoints.style.display = 'flex';
    summaryText.style.display = 'none';
    keypointsContainer.innerHTML = '';
    emptyStateKeypoints.style.display = 'none';

    try {
      const response = await fetch('http://localhost:3000/api/openai', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ conversation: lastConvo })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erreur serveur');
      }

      // Mettre à jour le quota
      quotaEl.textContent = `${data.quota.used}/${data.quota.limit}`;
      chrome.storage.local.set({
        used: data.quota.used,
        limit: data.quota.limit
      });

      // Afficher le résumé
      summaryText.textContent = data.summary;

      // Afficher les points clés
      renderKeypoints(data.keypoints || []);

    } catch (error) {
      summaryText.textContent = error.message;
      keypointsContainer.innerHTML = '';
      emptyStateKeypoints.style.display = 'block';
    } finally {
      // Masquer les états de chargement
      loadingSummary.style.display = 'none';
      loadingKeypoints.style.display = 'none';
      summaryText.style.display = 'block';
    }
  }

  // Rendre les points clés
  function renderKeypoints(keypoints) {
    keypointsContainer.innerHTML = '';

    if (keypoints.length === 0) {
      emptyStateKeypoints.style.display = 'block';
      return;
    }

    emptyStateKeypoints.style.display = 'none';

    keypoints.forEach((keypoint, index) => {
      const keypointEl = createKeypointElement(keypoint, index);
      keypointsContainer.appendChild(keypointEl);
    });
  }

  // Créer un élément point clé
  function createKeypointElement(keypoint, index) {
    const div = document.createElement('div');
    div.className = 'bg-white border border-gray-200 rounded-xl p-4 hover:border-anyset-200 hover:shadow-sm transition-all duration-200';

    div.innerHTML = `
      <div class="flex items-start space-x-3">
        <div class="flex-shrink-0 w-7 h-7 bg-anyset-100 text-anyset-700 rounded-full flex items-center justify-center text-sm font-semibold">
          ${index + 1}
        </div>
        <p class="flex-1 text-sm text-gray-700 leading-relaxed">${keypoint}</p>
        <button class="copy-btn flex-shrink-0 p-2 text-gray-400 hover:text-anyset-600 hover:bg-anyset-50 rounded-lg transition-all duration-200">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
          </svg>
        </button>
      </div>
    `;

    // Ajouter l'événement de copie
    const copyBtn = div.querySelector('.copy-btn');
    copyBtn.addEventListener('click', () => copyKeypoint(keypoint, copyBtn, index));

    return div;
  }

  // Copier un point clé
  async function copyKeypoint(keypoint, button, index) {
    try {
      await navigator.clipboard.writeText(keypoint);

      // Changer l'icône en checkmark
      button.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
        </svg>
      `;
      button.className = 'copy-btn flex-shrink-0 p-2 text-green-600 bg-green-50 rounded-lg transition-all duration-200';

      // Réinitialiser après 2 secondes
      if (copiedTimeouts.has(index)) {
        clearTimeout(copiedTimeouts.get(index));
      }

      const timeout = setTimeout(() => {
        button.innerHTML = `
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
          </svg>
        `;
        button.className = 'copy-btn flex-shrink-0 p-2 text-gray-400 hover:text-anyset-600 hover:bg-anyset-50 rounded-lg transition-all duration-200';
        copiedTimeouts.delete(index);
      }, 2000);

      copiedTimeouts.set(index, timeout);

    } catch (error) {
      console.error('Erreur lors de la copie:', error);
    }
  }

  // Fonction pour poser une question
  async function askQuestion() {
    const question = questionInput.value.trim();

    if (!question) {
      questionInput.focus();
      return;
    }

    if (!lastConvo) {
      alert('Aucune conversation détectée. Ouvrez une conversation Instagram d\'abord.');
      return;
    }

    // Afficher l'état de chargement
    loadingQuestion.style.display = 'flex';
    questionAnswer.style.display = 'none';
    askButton.disabled = true;

    try {
      const response = await fetch('http://localhost:3000/api/openai/question', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          conversation: lastConvo,
          question: question
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erreur serveur');
      }

      // Afficher la réponse
      answerText.textContent = data.answer;
      questionAnswer.style.display = 'block';
      questionInput.value = '';

    } catch (error) {
      alert('Erreur: ' + error.message);
    } finally {
      loadingQuestion.style.display = 'none';
      askButton.disabled = false;
    }
  }

  // Copier la réponse
  async function copyAnswer() {
    try {
      await navigator.clipboard.writeText(answerText.textContent);
      const originalText = copyAnswerBtn.innerHTML;
      copyAnswerBtn.innerHTML = `
        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
        </svg>
        <span>Copié !</span>
      `;

      setTimeout(() => {
        copyAnswerBtn.innerHTML = originalText;
      }, 2000);
    } catch (error) {
      console.error('Erreur lors de la copie:', error);
    }
  }

  // Event listeners pour les questions
  askButton.addEventListener('click', askQuestion);
  copyAnswerBtn.addEventListener('click', copyAnswer);

  questionInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
      askQuestion();
    }
  });

  // Écouter les changements de conversation
  chrome.storage.onChanged.addListener((changes, area) => {
    if (area === 'local' && changes.lastConvo) {
      lastConvo = changes.lastConvo.newValue;
      loadAI();
    }
  });

  // Initialiser l'application
  await initializeData();
});

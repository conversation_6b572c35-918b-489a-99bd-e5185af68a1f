<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0" />
  <title>Agent AI</title>
  <link rel="stylesheet" href="styles.css" />
</head>
<body class="bg-gray-50">
  <div id="app" class="w-80 min-h-96 bg-white shadow-lg border border-gray-200">
    <!-- Header -->
    <header class="bg-gradient-to-r from-anyset-600 to-anyset-700 text-white p-4">
      <div class="flex items-center">
        <!-- Zone logo (65%) -->
        <div class="flex items-center" style="width: 65%;">
          <!-- Logo Anyset Agent -->
          <img src="ANYSET AGENT LOGO TEXT.svg" alt="Anyset Agent" class="h-6 max-w-full object-contain" />
        </div>
        <!-- Zone quota (35%) -->
        <div class="flex justify-end" style="width: 35%;">
          <div id="quota" class="bg-white bg-opacity-20 px-3 py-1.5 rounded-full text-sm font-medium">
            0/100
          </div>
        </div>
      </div>
    </header>

    <!-- Content -->
    <div class="p-5 space-y-6">
      <!-- Summary Section -->
      <section>
        <div class="flex items-center space-x-2 mb-3">
          <!-- Hero Icon: Document Text -->
          <svg class="w-5 h-5 text-anyset-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
          <h2 class="text-sm font-semibold text-gray-800">Résumé</h2>
        </div>
        <div class="bg-gray-50 rounded-xl p-4 border border-gray-100">
          <div id="loading-summary" class="flex items-center space-x-3 text-gray-500" style="display: none;">
            <!-- Hero Icon: Loading (animated) -->
            <svg class="animate-spin w-5 h-5 text-anyset-600" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="text-sm">Analyse en cours...</span>
          </div>
          <p id="summary-text" class="text-gray-700 text-sm leading-relaxed">Aucune conversation détectée.</p>
        </div>
      </section>

      <!-- Points clés Section -->
      <section>
        <div class="flex items-center space-x-2 mb-4">
          <!-- Hero Icon: User Circle -->
          <svg class="w-5 h-5 text-anyset-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          <h2 class="text-sm font-semibold text-gray-800">Points clés du prospect</h2>
        </div>

        <div class="space-y-3">
          <div id="loading-keypoints" class="flex items-center space-x-3 text-gray-500" style="display: none;">
            <svg class="animate-spin w-5 h-5 text-anyset-600" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 818-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="text-sm">Analyse du prospect...</span>
          </div>

          <div id="keypoints-container">
            <!-- Les points clés seront ajoutés ici dynamiquement -->
          </div>

          <div id="empty-state-keypoints" class="text-center py-8">
            <!-- Hero Icon: User Circle -->
            <svg class="w-12 h-12 text-gray-300 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <p class="text-gray-500 text-sm">Aucune analyse disponible</p>
            <p class="text-gray-400 text-xs mt-1">Commencez une conversation sur Instagram</p>
          </div>
        </div>
      </section>

      <!-- Question Section -->
      <section>
        <div class="flex items-center space-x-2 mb-3">
          <!-- Hero Icon: Question Mark Circle -->
          <svg class="w-5 h-5 text-anyset-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          <h2 class="text-sm font-semibold text-gray-800">Poser une question</h2>
        </div>

        <div class="space-y-3">
          <div class="flex space-x-2">
            <input
              id="question-input"
              type="text"
              placeholder="Ex: Quel est le niveau d'intérêt du prospect ?"
              class="flex-1 px-3 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-anyset-500 focus:border-transparent"
            />
            <button
              id="ask-button"
              class="px-4 py-2 bg-anyset-600 text-white text-sm font-medium rounded-lg hover:bg-anyset-700 transition-colors duration-200 flex items-center space-x-2"
            >
              <!-- Hero Icon: Paper Airplane -->
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
              </svg>
              <span>Demander</span>
            </button>
          </div>

          <div id="loading-question" class="flex items-center space-x-3 text-gray-500" style="display: none;">
            <svg class="animate-spin w-5 h-5 text-anyset-600" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="text-sm">Analyse en cours...</span>
          </div>

          <div id="question-answer" class="bg-anyset-50 rounded-xl p-4 border border-anyset-100" style="display: none;">
            <div class="flex items-start space-x-3">
              <!-- Hero Icon: Chat Bubble Bottom Center Text -->
              <svg class="w-5 h-5 text-anyset-600 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"/>
              </svg>
              <div class="flex-1">
                <p id="answer-text" class="text-sm text-gray-700 leading-relaxed"></p>
                <button id="copy-answer" class="mt-2 text-xs text-anyset-600 hover:text-anyset-800 flex items-center space-x-1">
                  <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                  </svg>
                  <span>Copier la réponse</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- Chat flottant avec ChatGPT -->
    <div id="floating-chat" class="absolute bottom-3 left-3 right-3 bg-white border border-gray-200 rounded-2xl shadow-lg transition-all duration-300">
      <!-- État réduit -->
      <div id="chat-collapsed" class="flex items-center space-x-2 p-3">
        <div class="flex-1 flex items-center space-x-2">
          <!-- Hero Icon: Chat Bubble -->
          <svg class="w-5 h-5 text-anyset-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
          </svg>
          <input
            id="chat-input"
            type="text"
            placeholder="Poser une question sur le prospect..."
            class="flex-1 text-sm bg-transparent border-none outline-none placeholder-gray-400"
          />
        </div>
        <button
          id="send-button"
          class="w-8 h-8 bg-anyset-600 text-white rounded-full flex items-center justify-center hover:bg-anyset-700 transition-colors duration-200"
        >
          <!-- Hero Icon: Paper Airplane -->
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
          </svg>
        </button>
      </div>

      <!-- État étendu -->
      <div id="chat-expanded" class="hidden">
        <!-- Header du chat étendu -->
        <div class="flex items-center justify-between p-4 border-b border-gray-100">
          <div class="flex items-center space-x-2">
            <svg class="w-5 h-5 text-anyset-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
            </svg>
            <h3 class="text-sm font-semibold text-gray-800">Chat avec l'IA</h3>
          </div>
          <button id="collapse-chat" class="p-1 text-gray-400 hover:text-gray-600 rounded">
            <!-- Hero Icon: Chevron Down -->
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
            </svg>
          </button>
        </div>

        <!-- Messages du chat -->
        <div id="chat-messages" class="h-64 overflow-y-auto p-4 space-y-3">
          <!-- Les messages seront ajoutés ici dynamiquement -->
        </div>

        <!-- Input du chat étendu -->
        <div class="p-4 border-t border-gray-100">
          <div class="flex items-center space-x-2">
            <input
              id="chat-input-expanded"
              type="text"
              placeholder="Tapez votre message..."
              class="flex-1 px-3 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-anyset-500 focus:border-transparent"
            />
            <button
              id="send-button-expanded"
              class="w-10 h-10 bg-anyset-600 text-white rounded-full flex items-center justify-center hover:bg-anyset-700 transition-colors duration-200"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>

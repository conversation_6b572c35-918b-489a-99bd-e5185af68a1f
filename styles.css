@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Reset et base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  background-color: #f9fafb;
}

/* Utilitaires de base */
.w-80 { width: 20rem; }
.min-h-96 { min-height: 24rem; }
.w-8 { width: 2rem; }
.h-8 { height: 2rem; }
.w-5 { width: 1.25rem; }
.h-5 { height: 1.25rem; }
.w-7 { width: 1.75rem; }
.h-7 { height: 1.75rem; }
.w-8 { width: 2rem; }
.h-8 { height: 2rem; }
.w-4 { width: 1rem; }
.h-4 { height: 1rem; }
.w-6 { width: 1.5rem; }
.h-6 { height: 1.5rem; }
.w-12 { width: 3rem; }
.h-12 { height: 3rem; }

.max-w-full { max-width: 100%; }
.object-contain { object-fit: contain; }
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.justify-end { justify-content: flex-end; }

.bg-white { background-color: #ffffff; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-blue-100 { background-color: #dbeafe; }
.bg-blue-50 { background-color: #eff6ff; }
.bg-green-50 { background-color: #f0fdf4; }

.text-white { color: #ffffff; }
.text-gray-700 { color: #374151; }
.text-gray-800 { color: #1f2937; }
.text-gray-500 { color: #6b7280; }
.text-gray-400 { color: #9ca3af; }
.text-gray-300 { color: #d1d5db; }
.text-blue-600 { color: #2563eb; }
.text-blue-700 { color: #1d4ed8; }
.text-green-600 { color: #16a34a; }

/* Couleurs Anyset */
.text-anyset-600 { color: #2B2B44; }
.text-anyset-700 { color: #1f1f33; }
.text-anyset-800 { color: #161622; }
.text-anyset-500 { color: #3a3a55; }

.bg-anyset-50 { background-color: #f8f8fa; }
.bg-anyset-100 { background-color: #ededf0; }
.bg-anyset-600 { background-color: #2B2B44; }
.bg-anyset-700 { background-color: #1f1f33; }

.border-anyset-100 { border-color: #ededf0; }
.border-anyset-200 { border-color: #d9d9de; }

.border { border-width: 1px; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-100 { border-color: #f3f4f6; }
.border-blue-200 { border-color: #c3ddfd; }

.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-full { border-radius: 9999px; }

.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }

.p-4 { padding: 1rem; }
.p-5 { padding: 1.25rem; }
.p-3 { padding: 0.75rem; }
.p-2 { padding: 0.5rem; }
.p-1\.5 { padding: 0.375rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-1\.5 { padding-top: 0.375rem; padding-bottom: 0.375rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }

.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mt-1 { margin-top: 0.25rem; }
.mt-0\.5 { margin-top: 0.125rem; }
.mx-auto { margin-left: auto; margin-right: auto; }

.flex { display: flex; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }
.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-3 > * + * { margin-left: 0.75rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }

.flex-1 { flex: 1 1 0%; }
.flex-shrink-0 { flex-shrink: 0; }

.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-xs { font-size: 0.75rem; line-height: 1rem; }

.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }

.uppercase { text-transform: uppercase; }
.tracking-wide { letter-spacing: 0.025em; }

.leading-relaxed { line-height: 1.625; }

.text-center { text-align: center; }

.cursor-pointer { cursor: pointer; }

.transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }
.duration-200 { transition-duration: 200ms; }

.opacity-25 { opacity: 0.25; }
.opacity-75 { opacity: 0.75; }

.bg-opacity-20 { background-color: rgba(255, 255, 255, 0.2); }

/* Gradient */
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.from-blue-600 {
  --tw-gradient-from: #2563eb;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(37, 99, 235, 0));
}
.to-blue-700 {
  --tw-gradient-to: #1d4ed8;
}

/* Gradients Anyset */
.from-anyset-600 {
  --tw-gradient-from: #2B2B44;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(43, 43, 68, 0));
}
.to-anyset-700 {
  --tw-gradient-to: #1f1f33;
}

/* Animation */
@keyframes spin {
  to { transform: rotate(360deg); }
}
.animate-spin { animation: spin 1s linear infinite; }

/* Hover states */
.hover\:border-blue-200:hover { border-color: #c3ddfd; }
.hover\:border-anyset-200:hover { border-color: #d9d9de; }
.hover\:shadow-sm:hover { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.hover\:text-blue-600:hover { color: #2563eb; }
.hover\:text-anyset-600:hover { color: #2B2B44; }
.hover\:text-anyset-800:hover { color: #161622; }
.hover\:bg-blue-50:hover { background-color: #eff6ff; }
.hover\:bg-anyset-50:hover { background-color: #f8f8fa; }
.hover\:bg-anyset-700:hover { background-color: #1f1f33; }

/* Boutons */
button {
  border: none;
  background: none;
  cursor: pointer;
  outline: none;
}

button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Inputs */
input[type="text"] {
  border: 1px solid #e5e7eb;
  outline: none;
  font-family: inherit;
}

input[type="text"]:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Couleurs supplémentaires */
.bg-blue-600 { background-color: #2563eb; }
.bg-blue-700 { background-color: #1d4ed8; }
.hover\:bg-blue-700:hover { background-color: #1d4ed8; }

.focus\:ring-2:focus { box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1); }
.focus\:ring-blue-500:focus { box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1); }
.focus\:ring-anyset-500:focus { box-shadow: 0 0 0 3px rgba(43, 43, 68, 0.1); }
.focus\:border-transparent:focus { border-color: transparent; }

document.addEventListener('DOMContentLoaded', async function() {
  // Éléments DOM
  const quotaEl = document.getElementById('quota');
  const summaryText = document.getElementById('summary-text');
  const loadingSummary = document.getElementById('loading-summary');
  const loadingKeypoints = document.getElementById('loading-keypoints');
  const keypointsContainer = document.getElementById('keypoints-container');
  const emptyStateKeypoints = document.getElementById('empty-state-keypoints');

  // Éléments pour le chat flottant
  const floatingChat = document.getElementById('floating-chat');
  const chatCollapsed = document.getElementById('chat-collapsed');
  const chatExpanded = document.getElementById('chat-expanded');
  const chatInput = document.getElementById('chat-input');
  const sendButton = document.getElementById('send-button');
  const chatInputExpanded = document.getElementById('chat-input-expanded');
  const sendButtonExpanded = document.getElementById('send-button-expanded');
  const collapseChat = document.getElementById('collapse-chat');
  const chatMessages = document.getElementById('chat-messages');
  const contentDiv = document.querySelector('.p-5.space-y-6');

  let lastConvo = '';
  let chatHistory = [];
  let isChatExpanded = false;

  // Initialiser les données
  async function initializeData() {
    try {
      const data = await new Promise(resolve => {
        chrome.storage.local.get(['used', 'limit', 'lastConvo'], resolve);
      });

      // Mettre à jour le quota
      quotaEl.textContent = `${data.used || 0}/${data.limit || 100}`;
      lastConvo = data.lastConvo || '';

      console.log('Data initialized:', data);

      if (lastConvo) {
        await loadAI();
      }
    } catch (error) {
      console.error('Error initializing data:', error);
    }
  }

  // Charger l'IA
  async function loadAI() {
    if (!lastConvo) {
      summaryText.textContent = 'Aucune conversation détectée.';
      keypointsContainer.innerHTML = '';
      emptyStateKeypoints.style.display = 'block';
      return;
    }

    // Afficher les états de chargement
    loadingSummary.style.display = 'flex';
    loadingKeypoints.style.display = 'flex';
    summaryText.style.display = 'none';
    keypointsContainer.innerHTML = '';
    emptyStateKeypoints.style.display = 'none';

    try {
      const response = await fetch('http://localhost:3000/api/openai', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ conversation: lastConvo })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erreur serveur');
      }

      // Mettre à jour le quota
      quotaEl.textContent = `${data.quota.used}/${data.quota.limit}`;
      chrome.storage.local.set({
        used: data.quota.used,
        limit: data.quota.limit
      });

      // Afficher le résumé
      summaryText.textContent = data.summary;

      // Afficher les points clés
      renderKeypoints(data.keypoints || []);

    } catch (error) {
      summaryText.textContent = error.message;
      keypointsContainer.innerHTML = '';
      emptyStateKeypoints.style.display = 'block';
    } finally {
      // Masquer les états de chargement
      loadingSummary.style.display = 'none';
      loadingKeypoints.style.display = 'none';
      summaryText.style.display = 'block';
    }
  }

  // Rendre les points clés
  function renderKeypoints(keypoints) {
    keypointsContainer.innerHTML = '';

    if (keypoints.length === 0) {
      emptyStateKeypoints.style.display = 'block';
      return;
    }

    emptyStateKeypoints.style.display = 'none';

    keypoints.forEach((keypoint) => {
      const keypointEl = createKeypointElement(keypoint);
      keypointsContainer.appendChild(keypointEl);
    });
  }

  // Obtenir l'icône pour un type de point clé
  function getKeypointIcon(type) {
    const icons = {
      'PERSONNALITÉ': `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
      </svg>`,
      'BESOINS': `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v6a2 2 0 002 2h2m0 0h2a2 2 0 002-2V7a2 2 0 00-2-2H9m0 0V3m0 2v2"/>
      </svg>`,
      'MOTIVATIONS': `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
      </svg>`,
      'OBJECTIONS': `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
      </svg>`,

    };
    return icons[type] || `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
    </svg>`;
  }

  // Créer un élément point clé
  function createKeypointElement(keypoint) {
    const div = document.createElement('div');
    div.className = 'bg-white border border-gray-200 rounded-xl p-3 hover:border-anyset-200 hover:shadow-sm transition-all duration-200';

    // Parser le point clé pour extraire le type et la description
    const match = keypoint.match(/^([A-ZÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖØÙÚÛÜÝÞSS\s]+):\s*(.+)$/);
    const type = match ? match[1].trim() : '';
    const description = match ? match[2].trim() : keypoint;
    const icon = getKeypointIcon(type);

    div.innerHTML = `
      <div class="flex items-center space-x-3">
        <div class="flex-shrink-0 w-8 h-8 bg-anyset-100 text-anyset-600 rounded-lg flex items-center justify-center">
          ${icon}
        </div>
        <div class="flex-1">
          <div class="text-xs font-semibold text-anyset-700 uppercase tracking-wide">${type}</div>
          <div class="text-sm text-gray-700 mt-0.5">${description}</div>
        </div>
      </div>
    `;

    return div;
  }



  // Fonctions pour le chat flottant
  function toggleChat() {
    if (isChatExpanded) {
      collapseChatView();
    } else {
      expandChat();
    }
  }

  function expandChat() {
    chatCollapsed.classList.add('hidden');
    chatExpanded.classList.remove('hidden');
    isChatExpanded = true;

    // Ajuster l'espacement pour éviter l'overlap
    contentDiv.classList.remove('pb-12');
    contentDiv.classList.add('pb-80'); // Plus d'espace pour le chat étendu

    chatInputExpanded.focus();
  }

  function collapseChatView() {
    chatCollapsed.classList.remove('hidden');
    chatExpanded.classList.add('hidden');
    isChatExpanded = false;

    // Remettre l'espacement normal
    contentDiv.classList.remove('pb-80');
    contentDiv.classList.add('pb-12');
  }

  function addMessage(content, isUser = false) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `flex ${isUser ? 'justify-end' : 'justify-start'}`;

    messageDiv.innerHTML = `
      <div class="max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
        isUser
          ? 'bg-anyset-600 text-white'
          : 'bg-gray-100 text-gray-800'
      }">
        <p class="text-sm">${content}</p>
      </div>
    `;

    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;

    // Ajouter à l'historique
    chatHistory.push({ content, isUser });
  }

  async function sendMessage(message) {
    if (!message.trim()) return;

    if (!lastConvo) {
      addMessage('Aucune conversation Instagram détectée. Ouvrez une conversation d\'abord.', false);
      return;
    }

    // Ajouter le message de l'utilisateur
    addMessage(message, true);

    // Vider l'input
    if (isChatExpanded) {
      chatInputExpanded.value = '';
    } else {
      chatInput.value = '';
    }

    try {
      const response = await fetch('http://localhost:3000/api/openai/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          conversation: lastConvo,
          message: message,
          chatHistory: chatHistory.slice(-10) // Garder les 10 derniers messages
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erreur serveur');
      }

      // Ajouter la réponse de l'IA
      addMessage(data.response, false);

    } catch (error) {
      addMessage('Erreur: ' + error.message, false);
    }
  }

  // Event listeners pour le chat
  sendButton.addEventListener('click', () => {
    if (!isChatExpanded) {
      expandChat();
    } else {
      sendMessage(chatInput.value);
    }
  });

  sendButtonExpanded.addEventListener('click', () => {
    sendMessage(chatInputExpanded.value);
  });

  collapseChat.addEventListener('click', () => {
    collapseChatView();
  });

  chatInput.addEventListener('click', () => {
    if (!isChatExpanded) {
      expandChat();
    }
  });

  chatInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
      if (!isChatExpanded) {
        expandChat();
      } else {
        sendMessage(chatInput.value);
      }
    }
  });

  chatInputExpanded.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
      sendMessage(chatInputExpanded.value);
    }
  });

  // Écouter les changements de conversation
  chrome.storage.onChanged.addListener((changes, area) => {
    if (area === 'local' && changes.lastConvo) {
      lastConvo = changes.lastConvo.newValue;
      loadAI();
    }
  });

  // Initialiser l'application
  await initializeData();
});

const { createClient } = require('redis');

const LIMIT = parseInt(process.env.QUOTA_LIMIT || '100', 10);

// In-memory quota storage as fallback
const memoryQuota = new Map();

// Try to connect to Redis, but fallback to memory if it fails
let client = null;
let useRedis = false;

async function initializeRedis() {
  try {
    client = createClient({ url: process.env.REDIS_URL });
    await client.connect();
    useRedis = true;
    console.log('Redis connected successfully');
  } catch (error) {
    console.warn('Redis connection failed, using in-memory quota system:', error.message);
    useRedis = false;
  }
}

// Initialize Redis connection
initializeRedis();

module.exports = async (req, res, next) => {
  const key = `quota:${req.ip}:${new Date().toISOString().slice(0,10)}`;

  try {
    let used;

    if (useRedis && client) {
      // Use Redis if available
      used = await client.incr(key);
      if (used === 1) await client.expire(key, 86400);
    } else {
      // Use in-memory storage as fallback
      const currentDate = new Date().toISOString().slice(0,10);
      const memKey = `${req.ip}:${currentDate}`;

      if (!memoryQuota.has(memKey)) {
        memoryQuota.set(memKey, 0);
        // Clean up old entries (simple cleanup)
        setTimeout(() => memoryQuota.delete(memKey), 86400000); // 24 hours
      }

      used = memoryQuota.get(memKey) + 1;
      memoryQuota.set(memKey, used);
    }

    if (used > LIMIT) return res.status(429).json({ error: 'Quota dépassé' });
    res.locals.quota = { used, limit: LIMIT };
    next();

  } catch (error) {
    console.error('Quota limiter error:', error);
    // If there's an error, allow the request but log it
    res.locals.quota = { used: 1, limit: LIMIT };
    next();
  }
};

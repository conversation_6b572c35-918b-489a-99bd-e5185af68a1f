const { createApp } = Vue;

createApp({
  data() {
    return {
      // État de l'application
      loading: false,
      error: null,
      conversation: '',
      summary: '',
      suggestions: [],
      quota: {
        used: 0,
        limit: 100
      },
      copiedIndex: null
    }
  },
  
  async mounted() {
    // Initialiser l'application
    await this.initializeApp();
    
    // Écouter les changements de conversation
    this.setupConversationListener();
  },
  
  methods: {
    async initializeApp() {
      try {
        // Charger le quota actuel
        await this.loadQuota();
        
        // Charger la dernière conversation
        await this.loadLastConversation();
        
        // Si une conversation existe, analyser
        if (this.conversation) {
          await this.analyzeConversation();
        }
      } catch (error) {
        console.error('Erreur d\'initialisation:', error);
        this.error = 'Erreur d\'initialisation de l\'extension';
      }
    },
    
    async loadQuota() {
      return new Promise((resolve) => {
        chrome.storage.local.get(['used', 'limit'], (result) => {
          this.quota.used = result.used || 0;
          this.quota.limit = result.limit || 100;
          resolve();
        });
      });
    },
    
    async loadLastConversation() {
      return new Promise((resolve) => {
        chrome.storage.local.get('lastConvo', (result) => {
          this.conversation = result.lastConvo || '';
          resolve();
        });
      });
    },
    
    setupConversationListener() {
      chrome.storage.onChanged.addListener((changes, area) => {
        if (area === 'local' && changes.lastConvo) {
          this.conversation = changes.lastConvo.newValue || '';
          if (this.conversation) {
            this.analyzeConversation();
          }
        }
        
        if (area === 'local' && (changes.used || changes.limit)) {
          if (changes.used) this.quota.used = changes.used.newValue || 0;
          if (changes.limit) this.quota.limit = changes.limit.newValue || 100;
        }
      });
    },
    
    async analyzeConversation() {
      if (!this.conversation) {
        this.summary = '';
        this.suggestions = [];
        return;
      }
      
      this.loading = true;
      this.error = null;
      
      try {
        const response = await fetch('http://localhost:3000/api/openai', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            conversation: this.conversation
          })
        });
        
        const data = await response.json();
        
        if (!response.ok) {
          throw new Error(data.error || 'Erreur serveur');
        }
        
        // Mettre à jour les données
        this.summary = data.summary || '';
        this.suggestions = data.suggestions || [];
        
        // Mettre à jour le quota
        if (data.quota) {
          this.quota.used = data.quota.used;
          this.quota.limit = data.quota.limit;
          
          // Sauvegarder dans le storage Chrome
          chrome.storage.local.set({
            used: data.quota.used,
            limit: data.quota.limit
          });
        }
        
      } catch (error) {
        console.error('Erreur lors de l\'analyse:', error);
        this.error = error.message;
        this.summary = '';
        this.suggestions = [];
      } finally {
        this.loading = false;
      }
    },
    
    async copyToClipboard(text, index) {
      try {
        await navigator.clipboard.writeText(text);
        this.copiedIndex = index;
        
        // Réinitialiser l'état après 2 secondes
        setTimeout(() => {
          this.copiedIndex = null;
        }, 2000);
        
      } catch (error) {
        console.error('Erreur lors de la copie:', error);
        // Fallback pour les navigateurs qui ne supportent pas l'API clipboard
        this.fallbackCopyToClipboard(text, index);
      }
    },
    
    fallbackCopyToClipboard(text, index) {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      try {
        document.execCommand('copy');
        this.copiedIndex = index;
        setTimeout(() => {
          this.copiedIndex = null;
        }, 2000);
      } catch (error) {
        console.error('Erreur lors de la copie fallback:', error);
      }
      
      document.body.removeChild(textArea);
    },
    
    // Méthode utilitaire pour formater le quota
    getQuotaPercentage() {
      return Math.round((this.quota.used / this.quota.limit) * 100);
    },
    
    // Méthode pour déterminer la couleur du quota
    getQuotaColor() {
      const percentage = this.getQuotaPercentage();
      if (percentage >= 90) return 'text-red-600';
      if (percentage >= 70) return 'text-yellow-600';
      return 'text-green-600';
    }
  },
  
  computed: {
    quotaPercentage() {
      return this.getQuotaPercentage();
    },
    
    quotaColorClass() {
      return this.getQuotaColor();
    },
    
    hasConversation() {
      return this.conversation && this.conversation.trim().length > 0;
    },
    
    hasSuggestions() {
      return this.suggestions && this.suggestions.length > 0;
    }
  }
}).mount('#app');

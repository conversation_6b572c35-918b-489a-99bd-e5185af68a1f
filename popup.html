<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0" />
  <title>Agent AI</title>
  <link rel="stylesheet" href="popup.css" />
  <style>
    /* Bull<PERSON> de suggestions */
    .suggestions-list .suggestion-item {
      display: flex;
      align-items: center;
      background: #e0f7fa;
      border-radius: 16px;
      padding: 8px 12px;
      margin: 6px 0;
      position: relative;
    }
    .suggestions-list .suggestion-item::after {
      content: '';
      position: absolute;
      bottom: -6px;
      left: 16px;
      width: 0;
      height: 0;
      border-top: 6px solid #e0f7fa;
      border-right: 6px solid transparent;
    }
    .suggestions-list .suggestion-item strong {
      margin-right: 8px;
      color: #00796b;
    }
    .suggestions-list .suggestion-item span {
      flex: 1;
    }
    .suggestions-list .suggestion-item .copy-btn {
      margin-left: auto;
      padding: 2px 6px;
      font-size: 0.9em;
      border: none;
      background: #007bff;
      color: #fff;
      border-radius: 4px;
      cursor: pointer;
    }
    .suggestions-list .suggestion-item .copy-btn:active {
      transform: scale(0.95);
    }
  </style>
</head>
<body>
  <div class="container">
    <header class="header">
      <h1>Agent AI</h1>
      <div id="quota" class="quota">0/100</div>
    </header>

    <section>
      <h2>Résumé</h2>
      <div id="summary-content" class="summary-box">Chargement…</div>
    </section>

    <section>
      <h2>Suggestions</h2>
      <div id="suggestions-content" class="suggestions-list">Chargement…</div>
    </section>
  </div>

  <script src="popup.js"></script>
</body>
</html>

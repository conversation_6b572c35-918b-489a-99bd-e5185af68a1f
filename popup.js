// Attendre que Vue.js soit chargé
document.addEventListener('DOMContentLoaded', function() {
  const { createApp } = Vue;

  createApp({
    data() {
      return {
        quota: {
          used: 0,
          limit: 100
        },
        summary: 'Aucune conversation détectée.',
        suggestions: [],
        loading: false,
        copiedIndex: null,
        lastConvo: ''
      }
    },
    async mounted() {
      console.log('Vue app mounted');
      // Initialiser les données depuis le storage
      await this.initializeData();

      // Écouter les changements de conversation
      chrome.storage.onChanged.addListener((changes, area) => {
        if (area === 'local' && changes.lastConvo) {
          this.lastConvo = changes.lastConvo.newValue;
          this.loadAI();
        }
      });
    },
    methods: {
      async initializeData() {
        try {
          // Récupérer le quota et la conversation
          const data = await new Promise(resolve => {
            chrome.storage.local.get(['used', 'limit', 'lastConvo'], resolve);
          });

          this.quota.used = data.used || 0;
          this.quota.limit = data.limit || 100;
          this.lastConvo = data.lastConvo || '';

          console.log('Data initialized:', data);

          if (this.lastConvo) {
            await this.loadAI();
          }
        } catch (error) {
          console.error('Error initializing data:', error);
        }
      },

      async loadAI() {
        if (!this.lastConvo) {
          this.summary = 'Aucune conversation détectée.';
          this.suggestions = [];
          return;
        }

        this.loading = true;
        this.summary = '';
        this.suggestions = [];

        try {
          const response = await fetch('http://localhost:3000/api/openai', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ conversation: this.lastConvo })
          });

          const data = await response.json();

          if (!response.ok) {
            throw new Error(data.error || 'Erreur serveur');
          }

          // Mettre à jour le quota
          this.quota.used = data.quota.used;
          this.quota.limit = data.quota.limit;

          // Sauvegarder le quota dans le storage
          chrome.storage.local.set({
            used: data.quota.used,
            limit: data.quota.limit
          });

          // Afficher les résultats
          this.summary = data.summary;
          this.suggestions = data.suggestions || [];

        } catch (error) {
          this.summary = error.message;
          this.suggestions = [];
        } finally {
          this.loading = false;
        }
      },

      async copySuggestion(suggestion, index) {
        try {
          await navigator.clipboard.writeText(suggestion);
          this.copiedIndex = index;

          // Réinitialiser l'état après 2 secondes
          setTimeout(() => {
            this.copiedIndex = null;
          }, 2000);
        } catch (error) {
          console.error('Erreur lors de la copie:', error);
        }
      }
    }
  }).mount('#app');
});

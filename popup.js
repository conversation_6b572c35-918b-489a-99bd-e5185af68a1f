document.addEventListener('DOMContentLoaded', async function() {
  // Éléments DOM
  const quotaEl = document.getElementById('quota');
  const summaryText = document.getElementById('summary-text');
  const loadingSummary = document.getElementById('loading-summary');
  const loadingKeypoints = document.getElementById('loading-keypoints');
  const keypointsContainer = document.getElementById('keypoints-container');
  const emptyStateKeypoints = document.getElementById('empty-state-keypoints');

  // Éléments pour les questions
  const questionInput = document.getElementById('question-input');
  const askButton = document.getElementById('ask-button');
  const loadingQuestion = document.getElementById('loading-question');
  const questionAnswer = document.getElementById('question-answer');
  const answerText = document.getElementById('answer-text');
  const copyAnswerBtn = document.getElementById('copy-answer');

  let lastConvo = '';

  // Initialiser les données
  async function initializeData() {
    try {
      const data = await new Promise(resolve => {
        chrome.storage.local.get(['used', 'limit', 'lastConvo'], resolve);
      });

      // Mettre à jour le quota
      quotaEl.textContent = `${data.used || 0}/${data.limit || 100}`;
      lastConvo = data.lastConvo || '';

      console.log('Data initialized:', data);

      if (lastConvo) {
        await loadAI();
      }
    } catch (error) {
      console.error('Error initializing data:', error);
    }
  }

  // Charger l'IA
  async function loadAI() {
    if (!lastConvo) {
      summaryText.textContent = 'Aucune conversation détectée.';
      keypointsContainer.innerHTML = '';
      emptyStateKeypoints.style.display = 'block';
      return;
    }

    // Afficher les états de chargement
    loadingSummary.style.display = 'flex';
    loadingKeypoints.style.display = 'flex';
    summaryText.style.display = 'none';
    keypointsContainer.innerHTML = '';
    emptyStateKeypoints.style.display = 'none';

    try {
      const response = await fetch('http://localhost:3000/api/openai', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ conversation: lastConvo })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erreur serveur');
      }

      // Mettre à jour le quota
      quotaEl.textContent = `${data.quota.used}/${data.quota.limit}`;
      chrome.storage.local.set({
        used: data.quota.used,
        limit: data.quota.limit
      });

      // Afficher le résumé
      summaryText.textContent = data.summary;

      // Afficher les points clés
      renderKeypoints(data.keypoints || []);

    } catch (error) {
      summaryText.textContent = error.message;
      keypointsContainer.innerHTML = '';
      emptyStateKeypoints.style.display = 'block';
    } finally {
      // Masquer les états de chargement
      loadingSummary.style.display = 'none';
      loadingKeypoints.style.display = 'none';
      summaryText.style.display = 'block';
    }
  }

  // Rendre les points clés
  function renderKeypoints(keypoints) {
    keypointsContainer.innerHTML = '';

    if (keypoints.length === 0) {
      emptyStateKeypoints.style.display = 'block';
      return;
    }

    emptyStateKeypoints.style.display = 'none';

    keypoints.forEach((keypoint) => {
      const keypointEl = createKeypointElement(keypoint);
      keypointsContainer.appendChild(keypointEl);
    });
  }

  // Obtenir l'icône pour un type de point clé
  function getKeypointIcon(type) {
    const icons = {
      'PERSONNALITÉ': `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
      </svg>`,
      'BESOINS': `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v6a2 2 0 002 2h2m0 0h2a2 2 0 002-2V7a2 2 0 00-2-2H9m0 0V3m0 2v2"/>
      </svg>`,
      'MOTIVATIONS': `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
      </svg>`,
      'OBJECTIONS': `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
      </svg>`,

    };
    return icons[type] || `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
    </svg>`;
  }

  // Créer un élément point clé
  function createKeypointElement(keypoint) {
    const div = document.createElement('div');
    div.className = 'bg-white border border-gray-200 rounded-xl p-3 hover:border-anyset-200 hover:shadow-sm transition-all duration-200';

    // Parser le point clé pour extraire le type et la description
    const match = keypoint.match(/^([A-ZÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖØÙÚÛÜÝÞSS\s]+):\s*(.+)$/);
    const type = match ? match[1].trim() : '';
    const description = match ? match[2].trim() : keypoint;
    const icon = getKeypointIcon(type);

    div.innerHTML = `
      <div class="flex items-center space-x-3">
        <div class="flex-shrink-0 w-8 h-8 bg-anyset-100 text-anyset-600 rounded-lg flex items-center justify-center">
          ${icon}
        </div>
        <div class="flex-1">
          <div class="text-xs font-semibold text-anyset-700 uppercase tracking-wide">${type}</div>
          <div class="text-sm text-gray-700 mt-0.5">${description}</div>
        </div>
      </div>
    `;

    return div;
  }



  // Fonction pour poser une question
  async function askQuestion() {
    const question = questionInput.value.trim();

    if (!question) {
      questionInput.focus();
      return;
    }

    if (!lastConvo) {
      alert('Aucune conversation détectée. Ouvrez une conversation Instagram d\'abord.');
      return;
    }

    // Afficher l'état de chargement
    loadingQuestion.style.display = 'flex';
    questionAnswer.style.display = 'none';
    askButton.disabled = true;

    try {
      const response = await fetch('http://localhost:3000/api/openai/question', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          conversation: lastConvo,
          question: question
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erreur serveur');
      }

      // Afficher la réponse
      answerText.textContent = data.answer;
      questionAnswer.style.display = 'block';
      questionInput.value = '';

    } catch (error) {
      alert('Erreur: ' + error.message);
    } finally {
      loadingQuestion.style.display = 'none';
      askButton.disabled = false;
    }
  }

  // Copier la réponse
  async function copyAnswer() {
    try {
      await navigator.clipboard.writeText(answerText.textContent);
      const originalText = copyAnswerBtn.innerHTML;
      copyAnswerBtn.innerHTML = `
        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
        </svg>
        <span>Copié !</span>
      `;

      setTimeout(() => {
        copyAnswerBtn.innerHTML = originalText;
      }, 2000);
    } catch (error) {
      console.error('Erreur lors de la copie:', error);
    }
  }

  // Event listeners pour les questions
  askButton.addEventListener('click', askQuestion);
  copyAnswerBtn.addEventListener('click', copyAnswer);

  questionInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
      askQuestion();
    }
  });

  // Écouter les changements de conversation
  chrome.storage.onChanged.addListener((changes, area) => {
    if (area === 'local' && changes.lastConvo) {
      lastConvo = changes.lastConvo.newValue;
      loadAI();
    }
  });

  // Initialiser l'application
  await initializeData();
});

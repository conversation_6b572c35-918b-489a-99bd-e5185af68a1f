<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0" />
  <title>Agent AI</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    body { font-family: 'Inter', sans-serif; }
  </style>
</head>
<body class="bg-gray-50">
  <div id="app" class="w-80 min-h-96 bg-white shadow-lg border border-gray-200">
    <!-- Header -->
    <header class="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <!-- Hero Icon: Sparkles -->
          <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"/>
            </svg>
          </div>
          <h1 class="text-lg font-semibold">Agent AI</h1>
        </div>
        <div class="bg-white bg-opacity-20 px-3 py-1.5 rounded-full text-sm font-medium">
          {{ quota.used }}/{{ quota.limit }}
        </div>
      </div>
    </header>

    <!-- Content -->
    <div class="p-5 space-y-6">
      <!-- Summary Section -->
      <section>
        <div class="flex items-center space-x-2 mb-3">
          <!-- Hero Icon: Document Text -->
          <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
          <h2 class="text-sm font-semibold text-gray-800">Résumé</h2>
        </div>
        <div class="bg-gray-50 rounded-xl p-4 border border-gray-100">
          <div v-if="loading" class="flex items-center space-x-3 text-gray-500">
            <!-- Hero Icon: Loading (animated) -->
            <svg class="animate-spin w-5 h-5 text-blue-600" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="text-sm">Analyse en cours...</span>
          </div>
          <p v-else class="text-gray-700 text-sm leading-relaxed">{{ summary }}</p>
        </div>
      </section>

      <!-- Suggestions Section -->
      <section>
        <div class="flex items-center space-x-2 mb-4">
          <!-- Hero Icon: Light Bulb -->
          <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
          </svg>
          <h2 class="text-sm font-semibold text-gray-800">Suggestions</h2>
        </div>

        <div class="space-y-3">
          <div v-if="loading && suggestions.length === 0" class="flex items-center space-x-3 text-gray-500">
            <svg class="animate-spin w-5 h-5 text-blue-600" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="text-sm">Génération des suggestions...</span>
          </div>

          <div v-for="(suggestion, index) in suggestions" :key="index"
               class="bg-white border border-gray-200 rounded-xl p-4 hover:border-blue-200 hover:shadow-sm transition-all duration-200 group">
            <div class="flex items-start space-x-3">
              <div class="flex-shrink-0 w-7 h-7 bg-blue-100 text-blue-700 rounded-full flex items-center justify-center text-sm font-semibold">
                {{ index + 1 }}
              </div>
              <p class="flex-1 text-sm text-gray-700 leading-relaxed">{{ suggestion }}</p>
              <button @click="copySuggestion(suggestion, index)"
                      class="flex-shrink-0 p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
                      :class="{ 'text-green-600 bg-green-50': copiedIndex === index }">
                <!-- Hero Icon: Clipboard Copy / Check -->
                <svg v-if="copiedIndex !== index" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                </svg>
                <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
              </button>
            </div>
          </div>

          <div v-if="!loading && suggestions.length === 0" class="text-center py-8">
            <!-- Hero Icon: Chat Bubble Left Right -->
            <svg class="w-12 h-12 text-gray-300 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
            </svg>
            <p class="text-gray-500 text-sm">Aucune suggestion disponible</p>
            <p class="text-gray-400 text-xs mt-1">Commencez une conversation sur Instagram</p>
          </div>
        </div>
      </section>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>

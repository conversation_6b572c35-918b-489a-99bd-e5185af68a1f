// server/routes/openai.js
const express = require('express');
const OpenAI = require('openai');
const quota = require('../utils/quotaLimiter');
const { ragGenerate } = require('../utils/ragService');

// Configuration OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const router = express.Router();

router.post('/', quota, async (req, res) => {
  try {
    const { conversation } = req.body;

    // 1) Contexte via RAG
    const contextChunks = await ragGenerate(conversation);

    // 2) Résumé de la conversation
    const summaryResp = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'system', content: 'Tu es un assistant B2B expert en synthèse.' },
        ...contextChunks.map(chunk => ({ role: 'system', content: chunk })),
        { role: 'user', content: 'Résume cette conversation en une phrase concise.' }
      ],
      max_tokens: 60,
      temperature: 0.3,
    });
    const summary = summaryResp.choices[0].message.content.trim();

    // 3) Génération de 5 points clés définissant le prospect
    const keypointsResp = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'system', content: 'Tu es un expert en analyse comportementale et en profiling de prospects B2B. Tu analyses les conversations pour identifier les traits caractéristiques des prospects.' },
        { role: 'system', content: `Résumé : ${summary}` },
        { role: 'system', content: `Contexte complet : ${conversation}` },
        { role: 'user', content: `En te basant sur cette conversation, identifie exactement 5 points clés qui définissent cette personne en tant que prospect. Focus sur : personnalité, besoins, motivations, objections potentielles, et niveau de maturité.

IMPORTANT: Formate ta réponse comme ceci:
1. [Premier point clé]
2. [Deuxième point clé]
3. [Troisième point clé]
4. [Quatrième point clé]
5. [Cinquième point clé]

Chaque point doit être concis (1 phrase) et actionnable pour la prospection.` }
      ],
      max_tokens: 250,
      temperature: 0.7,
    });

    // Parser les points clés numérotés
    const rawKeypoints = keypointsResp.choices[0].message.content.trim();
    const keypoints = rawKeypoints
      .split('\n')
      .filter(line => line.trim().match(/^\d+\./))
      .map(line => line.replace(/^\d+\.\s*/, '').trim())
      .slice(0, 5);

    // 4) Envoi de la réponse
    res.json({ summary, keypoints, quota: res.locals.quota });

  } catch (error) {
    console.error('OpenAI error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Route pour les questions personnalisées
router.post('/question', quota, async (req, res) => {
  try {
    const { conversation, question } = req.body;

    if (!conversation || !question) {
      return res.status(400).json({ error: 'Conversation et question requises' });
    }

    // Contexte via RAG
    const contextChunks = await ragGenerate(conversation);

    // Réponse à la question personnalisée
    const questionResp = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'system', content: 'Tu es un expert en analyse de conversations B2B et en prospection commerciale. Tu analyses les conversations pour aider à comprendre les prospects et optimiser les stratégies de vente.' },
        ...contextChunks.map(chunk => ({ role: 'system', content: chunk })),
        { role: 'system', content: `Contexte de la conversation : ${conversation}` },
        { role: 'user', content: `En te basant sur cette conversation, réponds à cette question de manière précise et actionnable : ${question}` }
      ],
      max_tokens: 150,
      temperature: 0.7,
    });

    const answer = questionResp.choices[0].message.content.trim();

    res.json({ answer, quota: res.locals.quota });

  } catch (error) {
    console.error('Question OpenAI error:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;

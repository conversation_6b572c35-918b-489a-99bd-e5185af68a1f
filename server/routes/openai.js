// server/routes/openai.js
const express = require('express');
const OpenAI = require('openai');
const quota = require('../utils/quotaLimiter');
const { ragGenerate } = require('../utils/ragService');

// Configuration OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const router = express.Router();

router.post('/', quota, async (req, res) => {
  try {
    const { conversation } = req.body;

    // 1) Contexte via RAG
    const contextChunks = await ragGenerate(conversation);

    // 2) Résumé de la conversation
    const summaryResp = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'system', content: 'Tu es un assistant B2B expert en synthèse.' },
        ...contextChunks.map(chunk => ({ role: 'system', content: chunk })),
        { role: 'user', content: 'Résume cette conversation en une phrase concise.' }
      ],
      max_tokens: 60,
      temperature: 0.3,
    });
    const summary = summaryResp.choices[0].message.content.trim();

    // 3) Génération de 2 messages de closing concrets adaptés au contexte
    const closingResp = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'system', content: 'Tu es un expert en prospection et closing B2B. Ton objectif : closer un contrat rapidement. Réponds toujours avec professionnalisme et sens de l\'urgence.' },
        { role: 'system', content: `Résumé : ${summary}` },
        { role: 'system', content: `Contexte complet : ${conversation}` },
        { role: 'user', content: `En te basant sur le résumé : "${summary}" et sur le contexte complet de la conversation, propose exactement 2 réponses courtes (1–2 phrases chacune), pertinentes et orientées action, afin de faire avancer l’échange ou de conclure un accord.

IMPORTANT: Formate ta réponse comme ceci:
1. [Première suggestion]
2. [Deuxième suggestion]

Chaque suggestion doit être sur une ligne séparée et numérotée.` }
      ],
      max_tokens: 200,
      temperature: 0.8,
    });

    // Parser les suggestions numérotées
    const rawSuggestions = closingResp.choices[0].message.content.trim();
    const suggestions = rawSuggestions
      .split('\n')
      .filter(line => line.trim().match(/^\d+\./))
      .map(line => line.replace(/^\d+\.\s*/, '').trim())
      .slice(0, 2);

    // 4) Envoi de la réponse
    res.json({ summary, suggestions, quota: res.locals.quota });

  } catch (error) {
    console.error('OpenAI error:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;

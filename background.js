// background.js

chrome.runtime.onInstalled.addListener(() => {
  chrome.storage.local.set({ used: 0, limit: 100 });
});

// reset quotidien à 00:00:05
function scheduleReset() {
  const now = new Date();
  const next = new Date(now);
  next.setDate(now.getDate() + 1);
  next.setHours(0, 0, 5, 0);
  setTimeout(() => {
    chrome.storage.local.set({ used: 0 });
    scheduleReset();
  }, next - now);
}
scheduleReset();

// Écoute des messages du content script
chrome.runtime.onMessage.addListener((msg, sender) => {
  if (msg.action === 'updateConvo' && msg.convo) {
    chrome.storage.local.set({ lastConvo: msg.convo });
  }
});

(async () => {
  // Attendre que le DOM soit prêt
  if (document.readyState === 'loading') {
    await new Promise(res => document.addEventListener('DOMContentLoaded', res));
  }

  const quotaEl       = document.getElementById('quota');
  const summaryEl     = document.getElementById('summary-content');
  const suggestionsEl = document.getElementById('suggestions-content');
  if (!quotaEl || !summaryEl || !suggestionsEl) {
    console.error('Erreur d’initialisation de la popup');
    return;
  }

  let lastConvo = '';

  async function loadAI() {
    summaryEl.textContent     = 'Chargement…';
    suggestionsEl.textContent = 'Chargement…';

    // Affiche le quota actuel
    chrome.storage.local.get(['used','limit'], ({ used = 0, limit = 100 }) => {
      quotaEl.textContent = `${used}/${limit}`;
    });

    if (!lastConvo) {
      summaryEl.textContent     = 'Aucune conversation détectée.';
      suggestionsEl.textContent = '';
      return;
    }

    try {
      const resp = await fetch('http://localhost:3000/api/openai', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ conversation: lastConvo })
      });
      const data = await resp.json();
      if (!resp.ok) throw new Error(data.error || 'Erreur serveur');

      // Met à jour le quota
      chrome.storage.local.set({
        used:  data.quota.used,
        limit: data.quota.limit
      }, () => {
        quotaEl.textContent = `${data.quota.used}/${data.quota.limit}`;
      });

      // Affiche résumé
      summaryEl.textContent = data.summary;

      // Affiche suggestions dans des bulles numérotées
      suggestionsEl.innerHTML = '';
      (data.suggestions || []).forEach((txt, idx) => {
        const wrapper = document.createElement('div');
        wrapper.className = 'suggestion-item';

        const num = document.createElement('strong');
        num.textContent = `${idx + 1}. `;
        wrapper.appendChild(num);

        const span = document.createElement('span');
        span.textContent = txt;
        wrapper.appendChild(span);

        const btn = document.createElement('button');
        btn.textContent = 'Copier';
        btn.className = 'copy-btn';
        btn.addEventListener('click', () => {
          navigator.clipboard.writeText(txt);
          btn.textContent = '✔️';
        });
        wrapper.appendChild(btn);

        suggestionsEl.appendChild(wrapper);
      });
    } catch (err) {
      summaryEl.textContent     = err.message;
      suggestionsEl.textContent = '';
    }
  }

  // Récupère et écoute la conversation stockée
  chrome.storage.local.get('lastConvo', ({ lastConvo: convo = '' }) => {
    lastConvo = convo;
    loadAI();
  });
  chrome.storage.onChanged.addListener((changes, area) => {
    if (area === 'local' && changes.lastConvo) {
      lastConvo = changes.lastConvo.newValue;
      loadAI();
    }
  });
})();

// server/routes/openai.js
const express = require('express');
const OpenAI = require('openai');
const quota = require('../utils/quotaLimiter');
const { ragGenerate } = require('../utils/ragService');

// Configuration OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const router = express.Router();

router.post('/', quota, async (req, res) => {
  try {
    const { conversation } = req.body;

    // 1) Contexte via RAG
    const contextChunks = await ragGenerate(conversation);

    // 2) Résumé de la conversation
    const summaryResp = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'system', content: 'Tu es un assistant B2B expert en synthèse.' },
        ...contextChunks.map(chunk => ({ role: 'system', content: chunk })),
        { role: 'user', content: 'Résume cette conversation en une phrase concise.' }
      ],
      max_tokens: 60,
      temperature: 0.3,
    });
    const summary = summaryResp.choices[0].message.content.trim();

    // 3) Génération de 4 points clés définissant le prospect
    const keypointsResp = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'system', content: 'Tu es un expert en analyse comportementale et en profiling de prospects B2B. Tu analyses les conversations pour identifier les traits caractéristiques des prospects.' },
        { role: 'system', content: `Résumé : ${summary}` },
        { role: 'system', content: `Contexte complet : ${conversation}` },
        { role: 'user', content: `En te basant sur cette conversation, identifie exactement 4 points clés qui définissent cette personne en tant que prospect.

IMPORTANT: Formate ta réponse EXACTEMENT comme ceci:
1. PERSONNALITÉ: [Description courte en 3-5 mots]
2. BESOINS: [Description courte en 3-5 mots]
3. MOTIVATIONS: [Description courte en 3-5 mots]
4. OBJECTIONS: [Description courte en 3-5 mots]

Exemple:
1. PERSONNALITÉ: Analytique et méthodique
2. BESOINS: Optimisation des processus
3. MOTIVATIONS: Réduction des coûts
4. OBJECTIONS: Complexité technique` }
      ],
      max_tokens: 250,
      temperature: 0.7,
    });

    // Parser les points clés numérotés
    const rawKeypoints = keypointsResp.choices[0].message.content.trim();
    const keypoints = rawKeypoints
      .split('\n')
      .filter(line => line.trim().match(/^\d+\./))
      .map(line => line.replace(/^\d+\.\s*/, '').trim())
      .slice(0, 4);

    // 4) Envoi de la réponse
    res.json({ summary, keypoints, quota: res.locals.quota });

  } catch (error) {
    console.error('OpenAI error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Route pour le chat conversationnel
router.post('/chat', quota, async (req, res) => {
  try {
    const { conversation, message, chatHistory } = req.body;

    if (!conversation || !message) {
      return res.status(400).json({ error: 'Conversation et message requis' });
    }

    // Contexte via RAG
    const contextChunks = await ragGenerate(conversation);

    // Construire l'historique du chat pour le contexte
    const chatMessages = [
      { role: 'system', content: 'Tu es un expert en analyse de conversations B2B et en prospection commerciale. Tu aides à analyser les prospects et à optimiser les stratégies de vente. Réponds de manière concise et actionnable.' },
      ...contextChunks.map(chunk => ({ role: 'system', content: chunk })),
      { role: 'system', content: `Contexte de la conversation Instagram : ${conversation}` }
    ];

    // Ajouter l'historique du chat
    if (chatHistory && chatHistory.length > 0) {
      chatHistory.forEach(msg => {
        chatMessages.push({
          role: msg.isUser ? 'user' : 'assistant',
          content: msg.content
        });
      });
    }

    // Ajouter le message actuel
    chatMessages.push({ role: 'user', content: message });

    // Réponse de l'IA
    const chatResp = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: chatMessages,
      max_tokens: 200,
      temperature: 0.7,
    });

    const response = chatResp.choices[0].message.content.trim();

    res.json({ response, quota: res.locals.quota });

  } catch (error) {
    console.error('Chat OpenAI error:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
